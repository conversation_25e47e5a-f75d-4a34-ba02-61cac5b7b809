#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ptrade策略: 获取格力电器2025年7月30日9点35分股价
分钟级策略版本
"""

def initialize(context):
    """
    策略初始化函数
    """
    # 格力电器股票代码
    g.security = '000651.SZ'

    # 设置股票池
    set_universe(g.security)

    # 目标查询时间点
    g.target_date = '2025-07-30'
    g.target_times = ['09:35:00', '09:40:00', '09:45:00']
    g.found_data = {}  # 记录每个时间点是否已找到

    # 初始化每个时间点的查找状态
    for time_point in g.target_times:
        g.found_data[time_point] = False

def handle_data(context, data):
    """
    主策略函数 - 每分钟执行一次
    """
    security = g.security

    # 获取当前时间
    current_datetime = context.current_dt
    current_date_str = current_datetime.strftime('%Y-%m-%d')
    current_time_str = current_datetime.strftime('%H:%M:%S')
    current_full_str = current_datetime.strftime('%Y-%m-%d %H:%M:%S')

    print("当前时间: {}".format(current_full_str))

    # 检查是否是目标日期和目标时间点之一
    if current_date_str == g.target_date and current_time_str in g.target_times:
        # 检查这个时间点是否已经记录过
        if not g.found_data[current_time_str]:
            if security in data:
                current_data = data[security]

                print("=" * 60)
                print("*** 找到目标时间点! ***")

                # 根据时间显示不同的标题
                if current_time_str == '09:35:00':
                    print("格力电器2025年7月30日9点35分股价:")
                elif current_time_str == '09:40:00':
                    print("格力电器2025年7月30日9点40分股价:")
                elif current_time_str == '09:45:00':
                    print("格力电器2025年7月30日9点45分股价:")

                print("股价(收盘): {:.2f}元".format(current_data.close))
                print("开盘价: {:.2f}元".format(current_data.open))
                print("最高价: {:.2f}元".format(current_data.high))
                print("最低价: {:.2f}元".format(current_data.low))
                print("成交量: {:.0f}股".format(current_data.volume))
                print("=" * 60)

                # 标记这个时间点已找到
                g.found_data[current_time_str] = True

    # 检查是否所有时间点都已找到
    all_found = all(g.found_data[time_point] for time_point in g.target_times)
    if all_found:
        print("所有目标时间点数据已收集完成!")
        return
    
    # 如果是目标日期但不是目标时间，显示当前时间的数据
    if current_date_str == g.target_date and security in data:
        current_data = data[security]
        print("目标日期 - 当前时间: {} 股价: {:.2f}元".format(
            current_time_str, current_data.close))

    # 如果不是目标日期，尝试使用历史数据查找所有目标时间点
    if current_date_str != g.target_date:
        try:
            print("尝试获取历史分钟数据...")

            # 获取分钟级历史数据
            history_data = get_history(
                count=500,  # 获取500分钟数据
                unit='1m',  # 分钟级数据
                field=['open', 'close', 'high', 'low', 'volume'],
                security_list=security,
                fq='pre',
                include=True
            )

            print("获取到{}分钟历史数据".format(len(history_data)))

            # 查找所有目标时间点
            found_count = 0
            for date_idx in history_data.index:
                date_str = date_idx.strftime('%Y-%m-%d')
                time_str = date_idx.strftime('%H:%M:%S')

                if date_str == g.target_date and time_str in g.target_times:
                    # 检查这个时间点是否已经记录过
                    if not g.found_data[time_str]:
                        close_price = history_data['close'].loc[date_idx]
                        open_price = history_data['open'].loc[date_idx]
                        high_price = history_data['high'].loc[date_idx]
                        low_price = history_data['low'].loc[date_idx]
                        volume = history_data['volume'].loc[date_idx]

                        print("=" * 60)
                        print("*** 在历史数据中找到目标时间点! ***")

                        # 根据时间显示不同的标题
                        if time_str == '09:35:00':
                            print("格力电器2025年7月30日9点35分股价:")
                        elif time_str == '09:40:00':
                            print("格力电器2025年7月30日9点40分股价:")
                        elif time_str == '09:45:00':
                            print("格力电器2025年7月30日9点45分股价:")

                        print("股价(收盘): {:.2f}元".format(close_price))
                        print("开盘价: {:.2f}元".format(open_price))
                        print("最高价: {:.2f}元".format(high_price))
                        print("最低价: {:.2f}元".format(low_price))
                        print("成交量: {:.0f}股".format(volume))
                        print("=" * 60)

                        # 标记这个时间点已找到
                        g.found_data[time_str] = True
                        found_count += 1

            if found_count == 0:
                print("历史数据中未找到任何目标时间点")

                # 显示目标日期的部分数据
                print("目标日期的部分分钟数据:")
                count = 0
                for date_idx in history_data.index:
                    date_str = date_idx.strftime('%Y-%m-%d')
                    if date_str == g.target_date and count < 10:
                        time_str = date_idx.strftime('%H:%M:%S')
                        close_price = history_data['close'].loc[date_idx]
                        print("{} {}: {:.2f}元".format(date_str, time_str, close_price))
                        count += 1
            else:
                print("在历史数据中找到{}个目标时间点".format(found_count))

        except Exception as e:
            print("获取历史数据失败: {}".format(str(e)))

def before_trading_start(context, data):
    """
    盘前准备函数
    """
    print("策略启动 - 准备查询格力电器多个时间点股价")
    print("目标日期: {}".format(g.target_date))
    print("目标时间点: {}".format(", ".join(g.target_times)))

def after_trading_end(context, data):
    """
    盘后处理函数
    """
    # 统计找到的时间点数量
    found_count = sum(1 for found in g.found_data.values() if found)
    total_count = len(g.target_times)

    print("=" * 50)
    print("查询结果统计:")
    print("目标时间点总数: {}".format(total_count))
    print("成功找到: {}".format(found_count))

    # 显示每个时间点的状态
    for time_point in g.target_times:
        status = "✓ 已找到" if g.found_data[time_point] else "✗ 未找到"
        print("{}: {}".format(time_point, status))

    if found_count == total_count:
        print("所有目标时间点数据收集完成!")
    else:
        print("部分时间点数据未找到，请检查:")
        print("1. 2025年7月30日是否为交易日")
        print("2. 回测是否设置为分钟级")
        print("3. 回测时间范围是否包含所有目标时间")
    print("=" * 50)

"""
重要使用说明:

1. 在Ptrade中新建策略，复制此代码

2. 设置回测参数 (关键!):
   - 开始时间: 2025-07-30 09:30:00
   - 结束时间: 2025-07-30 15:00:00
   - 频率: 分钟级 (必须选择分钟级!)
   - 股票池: 000651.SZ

3. 运行回测，查看日志输出

4. 如果是实盘交易，需要:
   - 在2025年7月30日当天运行
   - 设置为分钟级策略
   - 在目标时间点会自动获取数据

注意事项:
- 必须设置为分钟级回测/交易
- 目标时间点:
  * 2025-07-30 09:35:00
  * 2025-07-30 09:40:00
  * 2025-07-30 09:45:00
- 格力电器代码: 000651.SZ
- 如果目标日期是非交易日，将无法获取数据
- 策略会自动收集所有三个时间点的数据

预期输出:
每个时间点都会显示:
- 股价(收盘价)
- 开盘价
- 最高价
- 最低价
- 成交量
"""
