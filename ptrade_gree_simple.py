#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ptrade策略: 获取格力电器2025年7月30日开盘价
简化版本，专门解决回测环境问题
"""

def initialize(context):
    """
    策略初始化函数
    """
    # 格力电器股票代码
    g.security = '000651.SZ'
    
    # 设置股票池
    set_universe(g.security)
    
    # 目标查询日期
    g.target_date = '2025-07-30'
    g.found_data = False

def handle_data(context, data):
    """
    主策略函数 - 获取格力电器开盘价
    """
    # 避免重复执行
    if g.found_data:
        return
        
    security = g.security
    
    print("=" * 50)
    print("查询格力电器2025年7月30日开盘价")
    print("=" * 50)
    
    # 方法1: 检查当前数据
    if security in data:
        current_data = data[security]
        current_date = context.current_dt.strftime('%Y-%m-%d')
        
        print("当前交易日: {}".format(current_date))
        print("当前开盘价: {:.2f}元".format(current_data.open))
        print("当前收盘价: {:.2f}元".format(current_data.close))
        
        # 检查是否是目标日期
        if current_date == g.target_date:
            print("*** 找到目标日期! ***")
            print("格力电器2025年7月30日开盘价: {:.2f}元".format(current_data.open))
            print("收盘价: {:.2f}元".format(current_data.close))
            print("最高价: {:.2f}元".format(current_data.high))
            print("最低价: {:.2f}元".format(current_data.low))
            print("成交量: {:.0f}股".format(current_data.volume))
            g.found_data = True
            return
    
    # 方法2: 使用get_history获取历史数据
    try:
        print("尝试获取历史数据...")
        
        # 获取历史数据
        history_data = get_history(
            count=60,  # 获取60天数据
            unit='1d', 
            field=['open', 'close', 'high', 'low', 'volume'],
            security_list=security,
            fq='pre',
            include=True
        )
        
        print("成功获取{}天历史数据".format(len(history_data)))
        print("数据范围: {} 到 {}".format(
            history_data.index[0].strftime('%Y-%m-%d'),
            history_data.index[-1].strftime('%Y-%m-%d')
        ))
        
        # 查找目标日期
        target_found = False
        for date_idx in history_data.index:
            date_str = date_idx.strftime('%Y-%m-%d')
            if date_str == g.target_date:
                open_price = history_data['open'].loc[date_idx]
                close_price = history_data['close'].loc[date_idx]
                high_price = history_data['high'].loc[date_idx]
                low_price = history_data['low'].loc[date_idx]
                volume = history_data['volume'].loc[date_idx]
                
                print("*** 在历史数据中找到目标日期! ***")
                print("格力电器2025年7月30日完整行情:")
                print("开盘价: {:.2f}元".format(open_price))
                print("收盘价: {:.2f}元".format(close_price))
                print("最高价: {:.2f}元".format(high_price))
                print("最低价: {:.2f}元".format(low_price))
                print("成交量: {:.0f}股".format(volume))
                target_found = True
                g.found_data = True
                break
        
        if not target_found:
            print("在历史数据中未找到2025年7月30日")
            print("可能原因:")
            print("1. 该日期不是交易日")
            print("2. 数据范围不包含该日期")
            print("3. 需要设置更大的回测时间范围")
            
            # 显示最近几天的数据作为参考
            print("\n最近5个交易日的开盘价:")
            count = 0
            for date_idx in reversed(history_data.index):
                if count >= 5:
                    break
                date_str = date_idx.strftime('%Y-%m-%d')
                open_price = history_data['open'].loc[date_idx]
                print("{}: {:.2f}元".format(date_str, open_price))
                count += 1
                
    except Exception as e:
        print("获取历史数据失败: {}".format(str(e)))
    
    # 方法3: 尝试使用get_price (如果支持的话)
    try:
        print("\n尝试使用get_price获取指定日期数据...")
        
        price_data = get_price(
            security_list=[security],
            start_date=g.target_date,
            end_date=g.target_date,
            frequency='1d',
            fields=['open', 'close', 'high', 'low', 'volume']
        )
        
        if price_data is not None and not price_data.empty:
            if security in price_data.columns:
                open_price = price_data[security]['open'].iloc[0]
                close_price = price_data[security]['close'].iloc[0]
                high_price = price_data[security]['high'].iloc[0]
                low_price = price_data[security]['low'].iloc[0]
                volume = price_data[security]['volume'].iloc[0]
                
                print("*** 使用get_price找到数据! ***")
                print("格力电器2025年7月30日行情:")
                print("开盘价: {:.2f}元".format(open_price))
                print("收盘价: {:.2f}元".format(close_price))
                print("最高价: {:.2f}元".format(high_price))
                print("最低价: {:.2f}元".format(low_price))
                print("成交量: {:.0f}股".format(volume))
                g.found_data = True
            else:
                print("get_price返回数据中未找到目标股票")
        else:
            print("get_price未返回数据")
            
    except Exception as e:
        print("get_price获取数据失败: {}".format(str(e)))
    
    print("=" * 50)

def before_trading_start(context, data):
    """
    盘前准备函数
    """
    print("策略启动 - 准备查询格力电器开盘价")

def after_trading_end(context, data):
    """
    盘后处理函数
    """
    if g.found_data:
        print("查询完成 - 已找到目标数据")
    else:
        print("查询完成 - 未找到目标日期数据")

"""
使用说明:
1. 在Ptrade中新建策略，复制此代码
2. 设置回测参数:
   - 开始时间: 2025-07-25 (确保包含目标日期)
   - 结束时间: 2025-08-05
   - 频率: 日线
3. 运行回测，查看日志输出

注意事项:
- 如果2025年7月30日是非交易日，将无法获取数据
- 确保回测时间范围包含目标日期
- 格力电器代码: 000651.SZ
"""
