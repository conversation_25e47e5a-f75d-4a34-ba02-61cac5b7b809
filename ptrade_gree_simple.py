#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ptrade策略: 获取格力电器2025年7月30日9点35分股价
简化版本，专门解决回测环境问题
"""

def initialize(context):
    """
    策略初始化函数
    """
    # 格力电器股票代码
    g.security = '000651.SZ'

    # 设置股票池
    set_universe(g.security)

    # 目标查询日期和时间
    g.target_date = '2025-07-30'
    g.target_time = '09:35:00'
    g.target_datetime = '2025-07-30 09:35:00'
    g.found_data = False

def handle_data(context, data):
    """
    主策略函数 - 获取格力电器9点35分股价
    """
    # 避免重复执行
    if g.found_data:
        return

    security = g.security

    print("=" * 50)
    print("查询格力电器2025年7月30日9点35分股价")
    print("=" * 50)

    # 方法1: 检查当前数据
    if security in data:
        current_data = data[security]
        current_datetime = context.current_dt.strftime('%Y-%m-%d %H:%M:%S')
        current_date = context.current_dt.strftime('%Y-%m-%d')
        current_time = context.current_dt.strftime('%H:%M:%S')

        print("当前时间: {}".format(current_datetime))
        print("当前价格: {:.2f}元".format(current_data.close))

        # 检查是否是目标日期和时间
        if current_date == g.target_date and current_time == g.target_time:
            print("*** 找到目标时间点! ***")
            print("格力电器2025年7月30日9点35分股价: {:.2f}元".format(current_data.close))
            print("开盘价: {:.2f}元".format(current_data.open))
            print("最高价: {:.2f}元".format(current_data.high))
            print("最低价: {:.2f}元".format(current_data.low))
            print("成交量: {:.0f}股".format(current_data.volume))
            g.found_data = True
            return
    
    # 方法2: 使用get_history获取分钟级历史数据
    try:
        print("尝试获取分钟级历史数据...")

        # 获取分钟级历史数据
        history_data = get_history(
            count=300,  # 获取300分钟数据（约2个交易日）
            unit='1m',  # 分钟级数据
            field=['open', 'close', 'high', 'low', 'volume'],
            security_list=security,
            fq='pre',
            include=True
        )

        print("成功获取{}分钟历史数据".format(len(history_data)))
        print("数据范围: {} 到 {}".format(
            history_data.index[0].strftime('%Y-%m-%d %H:%M:%S'),
            history_data.index[-1].strftime('%Y-%m-%d %H:%M:%S')
        ))

        # 查找目标日期和时间
        target_found = False
        for date_idx in history_data.index:
            datetime_str = date_idx.strftime('%Y-%m-%d %H:%M:%S')
            date_str = date_idx.strftime('%Y-%m-%d')
            time_str = date_idx.strftime('%H:%M:%S')

            # 检查是否是目标日期和时间
            if date_str == g.target_date and time_str == g.target_time:
                close_price = history_data['close'].loc[date_idx]
                open_price = history_data['open'].loc[date_idx]
                high_price = history_data['high'].loc[date_idx]
                low_price = history_data['low'].loc[date_idx]
                volume = history_data['volume'].loc[date_idx]

                print("*** 在历史数据中找到目标时间点! ***")
                print("格力电器2025年7月30日9点35分股价:")
                print("股价(收盘): {:.2f}元".format(close_price))
                print("开盘价: {:.2f}元".format(open_price))
                print("最高价: {:.2f}元".format(high_price))
                print("最低价: {:.2f}元".format(low_price))
                print("成交量: {:.0f}股".format(volume))
                target_found = True
                g.found_data = True
                break

        if not target_found:
            print("在历史数据中未找到2025年7月30日9点35分")
            print("可能原因:")
            print("1. 该日期不是交易日")
            print("2. 9点35分没有交易数据")
            print("3. 需要设置分钟级回测")

            # 显示目标日期的数据作为参考
            print("\n查找目标日期的所有分钟数据:")
            count = 0
            for date_idx in history_data.index:
                date_str = date_idx.strftime('%Y-%m-%d')
                if date_str == g.target_date and count < 10:
                    datetime_str = date_idx.strftime('%Y-%m-%d %H:%M:%S')
                    close_price = history_data['close'].loc[date_idx]
                    print("{}: {:.2f}元".format(datetime_str, close_price))
                    count += 1

    except Exception as e:
        print("获取分钟级历史数据失败: {}".format(str(e)))
    
    # 方法3: 尝试使用get_price获取分钟级数据
    try:
        print("\n尝试使用get_price获取分钟级数据...")

        # 尝试获取分钟级数据
        price_data = get_price(
            security_list=[security],
            start_date=g.target_date,
            end_date=g.target_date,
            frequency='1m',  # 分钟级数据
            fields=['open', 'close', 'high', 'low', 'volume']
        )

        if price_data is not None and not price_data.empty:
            if security in price_data.columns:
                # 查找9点35分的数据
                target_found = False
                for idx in price_data.index:
                    time_str = idx.strftime('%H:%M:%S')
                    if time_str == g.target_time:
                        close_price = price_data[security]['close'].loc[idx]
                        open_price = price_data[security]['open'].loc[idx]
                        high_price = price_data[security]['high'].loc[idx]
                        low_price = price_data[security]['low'].loc[idx]
                        volume = price_data[security]['volume'].loc[idx]

                        print("*** 使用get_price找到9点35分数据! ***")
                        print("格力电器2025年7月30日9点35分:")
                        print("股价(收盘): {:.2f}元".format(close_price))
                        print("开盘价: {:.2f}元".format(open_price))
                        print("最高价: {:.2f}元".format(high_price))
                        print("最低价: {:.2f}元".format(low_price))
                        print("成交量: {:.0f}股".format(volume))
                        g.found_data = True
                        target_found = True
                        break

                if not target_found:
                    print("get_price数据中未找到9点35分")
                    print("可用时间点:")
                    count = 0
                    for idx in price_data.index:
                        if count < 10:
                            time_str = idx.strftime('%H:%M:%S')
                            close_price = price_data[security]['close'].loc[idx]
                            print("{}: {:.2f}元".format(time_str, close_price))
                            count += 1
            else:
                print("get_price返回数据中未找到目标股票")
        else:
            print("get_price未返回数据")

    except Exception as e:
        print("get_price获取数据失败: {}".format(str(e)))

    print("=" * 50)

def before_trading_start(context, data):
    """
    盘前准备函数
    """
    print("策略启动 - 准备查询格力电器9点35分股价")

def after_trading_end(context, data):
    """
    盘后处理函数
    """
    if g.found_data:
        print("查询完成 - 已找到目标时间点数据")
    else:
        print("查询完成 - 未找到目标时间点数据")

"""
使用说明:
1. 在Ptrade中新建策略，复制此代码
2. 设置回测参数:
   - 开始时间: 2025-07-30 09:30:00
   - 结束时间: 2025-07-30 15:00:00
   - 频率: 分钟级 (重要!)
3. 运行回测，查看日志输出

注意事项:
- 必须设置为分钟级回测才能获取9点35分的数据
- 如果2025年7月30日是非交易日，将无法获取数据
- 确保回测时间范围包含目标时间点
- 格力电器代码: 000651.SZ
- 目标时间: 2025-07-30 09:35:00
"""
