#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
获取格力电器2025年7月30日开盘价
使用Ptrade API获取股票历史行情数据
"""

import pandas as pd
from datetime import datetime

def get_gree_open_price():
    """
    获取格力电器(000651.SZ)在2025年7月30日的开盘价
    """
    try:
        # 格力电器股票代码
        security = '000651.SZ'
        
        # 目标日期
        target_date = '20250730'
        
        print(f"正在获取格力电器({security})在{target_date}的开盘价...")
        
        # 方法1: 使用get_price获取指定日期的数据
        # 获取2025年7月30日当天的数据
        try:
            # 获取单日数据，包含开盘价
            price_data = get_price(
                security_list=[security],
                start_date='2025-07-30',
                end_date='2025-07-30',
                frequency='1d',
                fields=['open', 'close', 'high', 'low', 'volume']
            )
            
            if not price_data.empty and security in price_data.columns:
                open_price = price_data[security]['open'].iloc[0]
                print(f"格力电器2025年7月30日开盘价: {open_price}元")
                return open_price
            else:
                print("未找到指定日期的数据")
                return None
                
        except Exception as e:
            print(f"使用get_price获取数据失败: {e}")
            
        # 方法2: 使用get_history获取历史数据
        try:
            # 获取包含目标日期的历史数据
            history_data = get_history(
                count=5,  # 获取5天数据确保包含目标日期
                unit='1d',
                field='open',
                security_list=[security],
                fq='pre',  # 前复权
                include=True
            )
            
            # 查找2025年7月30日的数据
            target_datetime = pd.to_datetime('2025-07-30')
            
            if security in history_data.columns:
                # 找到最接近目标日期的数据
                for idx, date in enumerate(history_data.index):
                    if date.date() == target_datetime.date():
                        open_price = history_data[security].iloc[idx]
                        print(f"格力电器2025年7月30日开盘价: {open_price}元")
                        return open_price
                        
                print("在历史数据中未找到2025年7月30日的数据")
                print("可用的日期范围:")
                print(history_data.index)
                return None
            else:
                print(f"未找到股票代码{security}的数据")
                return None
                
        except Exception as e:
            print(f"使用get_history获取数据失败: {e}")
            
    except Exception as e:
        print(f"获取数据时发生错误: {e}")
        return None

def get_gree_daily_data():
    """
    获取格力电器指定日期的完整日线数据
    """
    try:
        security = '000651.SZ'
        
        # 获取2025年7月30日的完整数据
        daily_data = get_price(
            security_list=[security],
            start_date='2025-07-30',
            end_date='2025-07-30',
            frequency='1d',
            fields=['open', 'close', 'high', 'low', 'volume', 'money']
        )
        
        if not daily_data.empty and security in daily_data.columns:
            print("\n格力电器2025年7月30日完整行情数据:")
            print("-" * 50)
            data = daily_data[security].iloc[0]
            print(f"开盘价: {data['open']:.2f}元")
            print(f"收盘价: {data['close']:.2f}元") 
            print(f"最高价: {data['high']:.2f}元")
            print(f"最低价: {data['low']:.2f}元")
            print(f"成交量: {data['volume']:.0f}股")
            print(f"成交额: {data['money']:.2f}元")
            
            return data
        else:
            print("未获取到数据")
            return None
            
    except Exception as e:
        print(f"获取完整数据失败: {e}")
        return None

# 在Ptrade环境中运行的主函数
def main():
    """
    主函数 - 在Ptrade策略中调用
    """
    print("=" * 60)
    print("格力电器2025年7月30日开盘价查询")
    print("=" * 60)
    
    # 获取开盘价
    open_price = get_gree_open_price()
    
    if open_price is not None:
        print(f"\n✓ 成功获取开盘价: {open_price}元")
        
        # 获取完整数据
        get_gree_daily_data()
    else:
        print("\n✗ 未能获取到开盘价数据")
        print("\n可能的原因:")
        print("1. 2025年7月30日不是交易日")
        print("2. 该日期的数据尚未更新")
        print("3. 股票代码有误")
        print("4. 网络连接问题")

# 如果在Ptrade策略环境中使用，可以这样调用:
"""
def initialize(context):
    # 策略初始化
    pass

def handle_data(context, data):
    # 在策略中调用获取开盘价
    main()
"""

if __name__ == "__main__":
    # 直接运行脚本
    main()
