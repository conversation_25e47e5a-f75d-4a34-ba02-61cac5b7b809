#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ptrade策略: 获取格力电器2025年7月30日开盘价
适用于Ptrade量化交易平台
"""

def initialize(context):
    """
    策略初始化函数
    """
    # 格力电器股票代码
    g.security = '000651.SZ'
    
    # 设置股票池
    set_universe(g.security)
    
    # 目标查询日期
    g.target_date = '2025-07-30'
    
    log.info("策略初始化完成")
    log.info(f"目标股票: 格力电器({g.security})")
    log.info(f"查询日期: {g.target_date}")

def handle_data(context, data):
    """
    主策略函数 - 获取格力电器开盘价
    """
    try:
        security = g.security
        
        log.info("=" * 50)
        log.info("开始获取格力电器2025年7月30日开盘价")
        log.info("=" * 50)
        
        # 方法1: 使用get_history获取历史数据
        try:
            # 获取最近10天的开盘价数据
            history_data = get_history(
                count=10,
                unit='1d', 
                field='open',
                security_list=security,
                fq='pre',  # 前复权
                include=True
            )
            
            log.info("成功获取历史开盘价数据")
            log.info(f"数据日期范围: {history_data.index[0]} 到 {history_data.index[-1]}")
            
            # 查找目标日期的数据
            target_found = False
            for date_idx in history_data.index:
                if date_idx.strftime('%Y-%m-%d') == g.target_date:
                    open_price = history_data['open'].loc[date_idx]
                    log.info(f"✓ 找到目标日期数据!")
                    log.info(f"格力电器2025年7月30日开盘价: {open_price:.2f}元")
                    target_found = True
                    break
            
            if not target_found:
                log.info("✗ 未找到2025年7月30日的数据")
                log.info("可能原因: 该日期不是交易日或数据未更新")
                
                # 显示最近的交易日数据作为参考
                log.info("\n最近的交易日开盘价数据:")
                for i, (date_idx, price) in enumerate(history_data['open'].items()):
                    log.info(f"{date_idx.strftime('%Y-%m-%d')}: {price:.2f}元")
                    if i >= 4:  # 只显示最近5天
                        break
                        
        except Exception as e:
            log.info(f"获取历史数据失败: {e}")
            
        # 方法2: 使用get_price获取指定日期数据
        try:
            log.info("\n尝试使用get_price获取数据...")
            
            # 获取指定日期的完整行情数据
            price_data = get_price(
                security_list=[security],
                start_date=g.target_date,
                end_date=g.target_date,
                frequency='1d',
                fields=['open', 'close', 'high', 'low', 'volume']
            )
            
            if not price_data.empty:
                open_price = price_data[security]['open'].iloc[0]
                close_price = price_data[security]['close'].iloc[0]
                high_price = price_data[security]['high'].iloc[0]
                low_price = price_data[security]['low'].iloc[0]
                volume = price_data[security]['volume'].iloc[0]
                
                log.info("✓ 成功获取完整行情数据!")
                log.info(f"开盘价: {open_price:.2f}元")
                log.info(f"收盘价: {close_price:.2f}元")
                log.info(f"最高价: {high_price:.2f}元")
                log.info(f"最低价: {low_price:.2f}元")
                log.info(f"成交量: {volume:.0f}股")
            else:
                log.info("✗ get_price未获取到数据")
                
        except Exception as e:
            log.info(f"get_price获取数据失败: {e}")
            
        # 方法3: 获取当前实时数据作为参考
        try:
            log.info("\n获取当前实时数据作为参考...")
            
            # 获取当前快照数据
            snapshot = get_snapshot(security)
            if security in snapshot:
                current_price = snapshot[security]['last_px']
                open_today = snapshot[security]['open_px']
                
                log.info(f"今日开盘价: {open_today:.2f}元")
                log.info(f"当前价格: {current_price:.2f}元")
            else:
                log.info("未获取到实时快照数据")
                
        except Exception as e:
            log.info(f"获取实时数据失败: {e}")
            
        log.info("=" * 50)
        log.info("查询完成")
        log.info("=" * 50)
        
    except Exception as e:
        log.info(f"策略执行出错: {e}")

def before_trading_start(context, data):
    """
    盘前准备函数
    """
    log.info("盘前准备: 开始查询格力电器历史开盘价")

def after_trading_end(context, data):
    """
    盘后处理函数
    """
    log.info("盘后处理: 查询任务完成")

# 注意事项:
"""
使用说明:
1. 将此代码复制到Ptrade策略编辑器中
2. 保存策略并运行回测或交易
3. 查看日志输出获取开盘价信息

重要提醒:
- 如果2025年7月30日不是交易日，将无法获取到数据
- 确保Ptrade有足够的历史数据权限
- 格力电器代码为000651.SZ (深圳市场)
"""
