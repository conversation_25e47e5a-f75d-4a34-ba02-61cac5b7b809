#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ptrade策略: 获取格力电器2025年7月30日开盘价
适用于Ptrade量化交易平台
"""

def initialize(context):
    """
    策略初始化函数
    """
    # 格力电器股票代码
    g.security = '000651.SZ'

    # 设置股票池
    set_universe(g.security)

    # 目标查询日期
    g.target_date = '2025-07-30'

def handle_data(context, data):
    """
    主策略函数 - 获取格力电器开盘价
    """
    try:
        security = g.security

        # 方法1: 使用get_history获取历史数据
        try:
            # 获取包含目标日期的历史数据
            history_data = get_history(
                count=50,  # 获取50天数据确保包含目标日期
                unit='1d',
                field=['open', 'close', 'high', 'low', 'volume'],
                security_list=security,
                fq='pre',  # 前复权
                include=True
            )

            # 查找目标日期的数据
            target_found = False
            for date_idx in history_data.index:
                if date_idx.strftime('%Y-%m-%d') == g.target_date:
                    open_price = history_data['open'].loc[date_idx]
                    close_price = history_data['close'].loc[date_idx]
                    high_price = history_data['high'].loc[date_idx]
                    low_price = history_data['low'].loc[date_idx]
                    volume = history_data['volume'].loc[date_idx]

                    print("=" * 60)
                    print("找到目标日期数据!")
                    print("格力电器2025年7月30日完整行情:")
                    print("开盘价: {:.2f}元".format(open_price))
                    print("收盘价: {:.2f}元".format(close_price))
                    print("最高价: {:.2f}元".format(high_price))
                    print("最低价: {:.2f}元".format(low_price))
                    print("成交量: {:.0f}股".format(volume))
                    print("=" * 60)
                    target_found = True
                    break

            if not target_found:
                print("未找到2025年7月30日的数据")
                print("可能原因: 该日期不是交易日或数据未更新")

                # 显示最近的交易日数据作为参考
                print("最近的交易日开盘价数据:")
                recent_count = 0
                for date_idx in reversed(history_data.index):
                    if recent_count >= 5:
                        break
                    open_price = history_data['open'].loc[date_idx]
                    print("{}: {:.2f}元".format(date_idx.strftime('%Y-%m-%d'), open_price))
                    recent_count += 1

        except Exception as e:
            print("获取历史数据失败: {}".format(str(e)))

        # 方法2: 使用get_price获取指定日期数据
        try:
            # 获取指定日期的完整行情数据
            price_data = get_price(
                security_list=[security],
                start_date=g.target_date,
                end_date=g.target_date,
                frequency='1d',
                fields=['open', 'close', 'high', 'low', 'volume']
            )

            if price_data is not None and not price_data.empty and security in price_data.columns:
                open_price = price_data[security]['open'].iloc[0]
                close_price = price_data[security]['close'].iloc[0]
                high_price = price_data[security]['high'].iloc[0]
                low_price = price_data[security]['low'].iloc[0]
                volume = price_data[security]['volume'].iloc[0]

                print("=" * 60)
                print("使用get_price成功获取数据!")
                print("格力电器2025年7月30日行情:")
                print("开盘价: {:.2f}元".format(open_price))
                print("收盘价: {:.2f}元".format(close_price))
                print("最高价: {:.2f}元".format(high_price))
                print("最低价: {:.2f}元".format(low_price))
                print("成交量: {:.0f}股".format(volume))
                print("=" * 60)
            else:
                print("get_price未获取到数据")

        except Exception as e:
            print("get_price获取数据失败: {}".format(str(e)))

    except Exception as e:
        print("策略执行出错: {}".format(str(e)))

def before_trading_start(context, data):
    """
    盘前准备函数
    """
    print("开始查询格力电器2025年7月30日开盘价...")

def after_trading_end(context, data):
    """
    盘后处理函数
    """
    print("查询任务完成")

# 使用说明:
# 1. 将此代码复制到Ptrade策略编辑器中
# 2. 设置回测时间范围包含2025年7月30日前后
# 3. 保存策略并运行回测
# 4. 查看日志输出获取开盘价信息
