#coding:gbk
"""
QMT策略: 获取格力电器2025年7月30日三个时间点股价
时间点: 9:35, 9:40, 9:45
适用于QMT量化交易平台
"""

def init(ContextInfo):
    """
    初始化函数
    """
    # 格力电器股票代码
    ContextInfo.security = '000651.SZ'
    
    # 设置股票池
    ContextInfo.set_universe([ContextInfo.security])
    
    # 目标查询时间点
    ContextInfo.target_date = '2025-07-30'
    ContextInfo.target_times = ['09:35:00', '09:40:00', '09:45:00']
    ContextInfo.time_labels = ['9点35分', '9点40分', '9点45分']
    
    # 记录每个时间点是否已找到
    ContextInfo.found_data = {}
    # 存储获取到的股价数据
    ContextInfo.stock_data = {}
    
    for time_point in ContextInfo.target_times:
        ContextInfo.found_data[time_point] = False
        ContextInfo.stock_data[time_point] = None
    
    print("QMT策略初始化完成")
    print("目标股票: 格力电器({})".format(ContextInfo.security))
    print("目标日期: {}".format(ContextInfo.target_date))
    print("目标时间点: {}".format(", ".join(ContextInfo.target_times)))

def handlebar(ContextInfo):
    """
    主策略函数 - 每分钟执行一次
    """
    security = ContextInfo.security
    
    # 获取当前时间
    current_datetime = timetag_to_datetime(ContextInfo.get_bar_timetag(security))
    current_date_str = current_datetime.strftime('%Y-%m-%d')
    current_time_str = current_datetime.strftime('%H:%M:%S')
    current_full_str = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
    
    print("当前时间: {}".format(current_full_str))
    
    # 检查是否是目标日期和目标时间点之一
    if current_date_str == ContextInfo.target_date and current_time_str in ContextInfo.target_times:
        # 检查这个时间点是否已经记录过
        if not ContextInfo.found_data[current_time_str]:
            # 获取当前行情数据
            market_data = ContextInfo.get_market_data(['open', 'close', 'high', 'low', 'volume'], 
                                                     [security], 
                                                     period='1m', 
                                                     dividend_type='none', 
                                                     count=1)
            
            if market_data and security in market_data:
                current_data = market_data[security]
                
                # 获取时间标签
                time_index = ContextInfo.target_times.index(current_time_str)
                time_label = ContextInfo.time_labels[time_index]
                
                print("=" * 60)
                print("*** 找到目标时间点! ***")
                print("格力电器2025年7月30日{}股价:".format(time_label))
                print("时间: {}".format(current_full_str))
                print("股价(收盘): {:.2f}元".format(current_data['close'][-1]))
                print("开盘价: {:.2f}元".format(current_data['open'][-1]))
                print("最高价: {:.2f}元".format(current_data['high'][-1]))
                print("最低价: {:.2f}元".format(current_data['low'][-1]))
                print("成交量: {:.0f}股".format(current_data['volume'][-1]))
                print("=" * 60)
                
                # 保存数据到字典
                ContextInfo.stock_data[current_time_str] = {
                    'time': current_full_str,
                    'time_label': time_label,
                    'close': current_data['close'][-1],
                    'open': current_data['open'][-1],
                    'high': current_data['high'][-1],
                    'low': current_data['low'][-1],
                    'volume': current_data['volume'][-1]
                }
                
                # 标记这个时间点已找到
                ContextInfo.found_data[current_time_str] = True
                
                # 立即保存到文件
                save_data_to_file(ContextInfo, current_time_str)
                
                # 检查是否所有时间点都已找到
                all_found = all(ContextInfo.found_data[time_point] for time_point in ContextInfo.target_times)
                if all_found:
                    print("🎉 所有目标时间点数据已收集完成!")
                    save_summary(ContextInfo)
    
    # 如果不是目标日期，尝试使用历史数据查找
    elif current_date_str != ContextInfo.target_date:
        # 只在第一次执行时查找历史数据
        if not any(ContextInfo.found_data.values()):
            search_historical_data(ContextInfo, security)

def save_data_to_file(ContextInfo, time_point):
    """
    保存单个时间点的数据到文件
    """
    try:
        data = ContextInfo.stock_data[time_point]
        if data is None:
            return
        
        # 创建文件名
        time_str = time_point.replace(':', '')
        filename = "gree_stock_{}_{}.txt".format(ContextInfo.target_date.replace('-', ''), time_str)
        
        # 保存到文本文件
        with open(filename, 'w', encoding='utf-8') as f:
            f.write("格力电器股价数据\n")
            f.write("=" * 30 + "\n")
            f.write("股票代码: {}\n".format(ContextInfo.security))
            f.write("股票名称: 格力电器\n")
            f.write("日期: {}\n".format(ContextInfo.target_date))
            f.write("时间: {}\n".format(time_point))
            f.write("时间标签: {}\n".format(data['time_label']))
            f.write("完整时间: {}\n".format(data['time']))
            f.write("-" * 30 + "\n")
            f.write("股价数据:\n")
            f.write("收盘价: {:.2f}元\n".format(data['close']))
            f.write("开盘价: {:.2f}元\n".format(data['open']))
            f.write("最高价: {:.2f}元\n".format(data['high']))
            f.write("最低价: {:.2f}元\n".format(data['low']))
            f.write("成交量: {:.0f}股\n".format(data['volume']))
            f.write("=" * 30 + "\n")
        
        print("💾 数据已保存到文件: {}".format(filename))
        
        # 同时保存到CSV格式
        save_to_csv(ContextInfo, time_point, data)
        
    except Exception as e:
        print("保存数据失败: {}".format(str(e)))

def save_to_csv(ContextInfo, time_point, data):
    """
    保存数据到CSV文件
    """
    try:
        # CSV文件名
        csv_filename = "gree_stock_data_{}.csv".format(ContextInfo.target_date.replace('-', ''))
        
        # 尝试读取现有文件来检查是否存在
        file_exists = False
        try:
            with open(csv_filename, 'r', encoding='utf-8') as f:
                file_exists = True
        except:
            file_exists = False
        
        with open(csv_filename, 'a', encoding='utf-8') as f:
            if not file_exists:
                # 写入表头
                f.write("股票代码,股票名称,日期,时间,时间标签,收盘价,开盘价,最高价,最低价,成交量\n")
            
            # 写入数据
            f.write("{},{},{},{},{},{:.2f},{:.2f},{:.2f},{:.2f},{:.0f}\n".format(
                ContextInfo.security,
                '格力电器',
                ContextInfo.target_date,
                time_point,
                data['time_label'],
                data['close'],
                data['open'],
                data['high'],
                data['low'],
                data['volume']
            ))
        
        if not file_exists:
            print("📊 CSV文件已创建: {}".format(csv_filename))
        else:
            print("📊 数据已追加到CSV文件: {}".format(csv_filename))
            
    except Exception as e:
        print("保存CSV失败: {}".format(str(e)))

def save_summary(ContextInfo):
    """
    保存汇总数据
    """
    try:
        summary_filename = "gree_stock_summary_{}.txt".format(ContextInfo.target_date.replace('-', ''))
        
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write("格力电器股价数据汇总\n")
            f.write("=" * 50 + "\n")
            f.write("股票代码: {}\n".format(ContextInfo.security))
            f.write("股票名称: 格力电器\n")
            f.write("目标日期: {}\n".format(ContextInfo.target_date))
            f.write("收集时间点数: {}\n".format(len(ContextInfo.target_times)))
            f.write("成功收集数: {}\n".format(sum(1 for found in ContextInfo.found_data.values() if found)))
            f.write("-" * 50 + "\n")
            
            for i, time_point in enumerate(ContextInfo.target_times):
                time_label = ContextInfo.time_labels[i]
                f.write("\n{} ({})\n".format(time_label, time_point))
                
                if ContextInfo.found_data[time_point] and ContextInfo.stock_data[time_point]:
                    data = ContextInfo.stock_data[time_point]
                    f.write("状态: ✅ 已获取\n")
                    f.write("收盘价: {:.2f}元\n".format(data['close']))
                    f.write("开盘价: {:.2f}元\n".format(data['open']))
                    f.write("最高价: {:.2f}元\n".format(data['high']))
                    f.write("最低价: {:.2f}元\n".format(data['low']))
                    f.write("成交量: {:.0f}股\n".format(data['volume']))
                else:
                    f.write("状态: ❌ 未获取\n")
                f.write("-" * 30 + "\n")
            
            f.write("=" * 50 + "\n")
        
        print("📋 汇总数据已保存到: {}".format(summary_filename))
        
    except Exception as e:
        print("保存汇总数据失败: {}".format(str(e)))

def search_historical_data(ContextInfo, security):
    """
    搜索历史数据中的目标时间点
    """
    try:
        print("正在搜索历史数据中的目标时间点...")
        
        # 获取分钟级历史数据
        history_data = ContextInfo.get_history_data(
            count=600,  # 获取600分钟数据
            period='1m',
            dividend_type='none',
            field=['open', 'close', 'high', 'low', 'volume'],
            stock_list=[security]
        )
        
        if history_data and security in history_data:
            stock_history = history_data[security]
            print("获取到{}分钟历史数据".format(len(stock_history['close'])))
            
            # 查找所有目标时间点
            found_times = []
            for i, timestamp in enumerate(stock_history['time']):
                date_time = timetag_to_datetime(timestamp)
                date_str = date_time.strftime('%Y-%m-%d')
                time_str = date_time.strftime('%H:%M:%S')
                
                if date_str == ContextInfo.target_date and time_str in ContextInfo.target_times:
                    if not ContextInfo.found_data[time_str]:
                        # 获取数据
                        close_price = stock_history['close'][i]
                        open_price = stock_history['open'][i]
                        high_price = stock_history['high'][i]
                        low_price = stock_history['low'][i]
                        volume = stock_history['volume'][i]
                        
                        # 获取时间标签
                        time_index = ContextInfo.target_times.index(time_str)
                        time_label = ContextInfo.time_labels[time_index]
                        
                        print("=" * 60)
                        print("*** 在历史数据中找到目标时间点! ***")
                        print("格力电器2025年7月30日{}股价:".format(time_label))
                        print("时间: {} {}".format(date_str, time_str))
                        print("股价(收盘): {:.2f}元".format(close_price))
                        print("开盘价: {:.2f}元".format(open_price))
                        print("最高价: {:.2f}元".format(high_price))
                        print("最低价: {:.2f}元".format(low_price))
                        print("成交量: {:.0f}股".format(volume))
                        print("=" * 60)
                        
                        # 保存数据到字典
                        ContextInfo.stock_data[time_str] = {
                            'time': "{} {}".format(date_str, time_str),
                            'time_label': time_label,
                            'close': close_price,
                            'open': open_price,
                            'high': high_price,
                            'low': low_price,
                            'volume': volume
                        }
                        
                        # 标记已找到
                        ContextInfo.found_data[time_str] = True
                        found_times.append(time_str)
                        
                        # 立即保存到文件
                        save_data_to_file(ContextInfo, time_str)
            
            if found_times:
                print("在历史数据中找到{}个目标时间点: {}".format(
                    len(found_times), ", ".join(found_times)))
                
                # 检查是否所有时间点都找到了
                all_found = all(ContextInfo.found_data[time_point] for time_point in ContextInfo.target_times)
                if all_found:
                    print("🎉 所有目标时间点数据已收集完成!")
                    save_summary(ContextInfo)
            else:
                print("❌ 历史数据中未找到任何目标时间点")
        else:
            print("未获取到历史数据")
            
    except Exception as e:
        print("获取历史数据失败: {}".format(str(e)))

"""
🔧 QMT使用说明:

1. 📋 在QMT中新建Python策略，复制此代码

2. ⚙️  设置策略参数:
   - 默认周期: 1分钟
   - 默认品种: 000651.SZ
   - 运行位置: 副图

3. ▶️  运行策略:
   - 点击"运行"按钮启动策略
   - 查看日志输出获取股价信息

4. 💾 数据保存:
   - TXT文件: 详细的文本格式数据
   - CSV文件: 便于Excel打开的表格数据
   - 汇总文件: 包含所有时间点的总结

⚠️  注意事项:
- 必须设置为分钟级运行
- 格力电器代码: 000651.SZ
- 目标日期: 2025-07-30
- 目标时间: 09:35, 09:40, 09:45
- 数据会自动保存到QMT运行目录
- 如果目标日期不是交易日，将无法获取数据

📊 预期输出:
每个时间点显示:
- 股价(收盘价)
- 开盘价
- 最高价
- 最低价
- 成交量
"""
