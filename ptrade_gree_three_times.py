#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Ptrade策略: 获取格力电器2025年7月30日三个时间点股价
时间点: 9:35, 9:40, 9:45
"""

def initialize(context):
    """
    策略初始化函数
    """
    # 格力电器股票代码
    g.security = '000651.SZ'

    # 设置股票池
    set_universe(g.security)

    # 目标查询时间点
    g.target_date = '2025-07-30'
    g.target_times = ['09:35:00', '09:40:00', '09:45:00']
    g.time_labels = ['9点35分', '9点40分', '9点45分']

    # 记录每个时间点是否已找到
    g.found_data = {}
    # 存储获取到的股价数据
    g.stock_data = {}

    for time_point in g.target_times:
        g.found_data[time_point] = False
        g.stock_data[time_point] = None

    # 获取研究路径用于保存文件
    try:
        g.save_path = get_research_path()
        print("数据保存路径: {}".format(g.save_path))
    except:
        g.save_path = ""
        print("无法获取保存路径，将尝试保存到当前目录")

def handle_data(context, data):
    """
    主策略函数 - 每分钟执行一次
    """
    security = g.security
    
    # 获取当前时间
    current_datetime = context.current_dt
    current_date_str = current_datetime.strftime('%Y-%m-%d')
    current_time_str = current_datetime.strftime('%H:%M:%S')
    current_full_str = current_datetime.strftime('%Y-%m-%d %H:%M:%S')
    
    # 检查是否是目标日期和目标时间点之一
    if current_date_str == g.target_date and current_time_str in g.target_times:
        # 检查这个时间点是否已经记录过
        if not g.found_data[current_time_str] and security in data:
            current_data = data[security]
            
            # 获取时间标签
            time_index = g.target_times.index(current_time_str)
            time_label = g.time_labels[time_index]
            
            print("=" * 60)
            print("*** 找到目标时间点! ***")
            print("格力电器2025年7月30日{}股价:".format(time_label))
            print("时间: {}".format(current_full_str))
            print("股价(收盘): {:.2f}元".format(current_data.close))
            print("开盘价: {:.2f}元".format(current_data.open))
            print("最高价: {:.2f}元".format(current_data.high))
            print("最低价: {:.2f}元".format(current_data.low))
            print("成交量: {:.0f}股".format(current_data.volume))
            print("=" * 60)

            # 保存数据到字典
            g.stock_data[current_time_str] = {
                'time': current_full_str,
                'time_label': time_label,
                'close': current_data.close,
                'open': current_data.open,
                'high': current_data.high,
                'low': current_data.low,
                'volume': current_data.volume
            }

            # 标记这个时间点已找到
            g.found_data[current_time_str] = True

            # 立即保存到文件
            save_data_to_file(current_time_str)
            
            # 检查是否所有时间点都已找到
            all_found = all(g.found_data[time_point] for time_point in g.target_times)
            if all_found:
                print("🎉 所有目标时间点数据已收集完成!")
                print_summary()
    
    # 如果不是目标日期，尝试使用历史数据查找
    elif current_date_str != g.target_date:
        # 只在第一次执行时查找历史数据
        if not any(g.found_data.values()):
            search_historical_data(security)

def save_data_to_file(time_point):
    """
    保存单个时间点的数据到文件
    """
    try:
        import json
        import os
        from datetime import datetime

        # 获取数据
        data = g.stock_data[time_point]
        if data is None:
            return

        # 创建文件名
        time_str = time_point.replace(':', '')  # 去掉冒号，避免文件名问题
        filename = "gree_stock_{}_{}.json".format(g.target_date.replace('-', ''), time_str)

        # 完整文件路径
        if g.save_path:
            filepath = os.path.join(g.save_path, filename)
        else:
            filepath = filename

        # 准备保存的数据
        save_data = {
            'stock_code': g.security,
            'stock_name': '格力电器',
            'date': g.target_date,
            'time': time_point,
            'time_label': data['time_label'],
            'datetime': data['time'],
            'price_data': {
                'close': float(data['close']),
                'open': float(data['open']),
                'high': float(data['high']),
                'low': float(data['low']),
                'volume': float(data['volume'])
            },
            'save_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        # 保存到JSON文件
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(save_data, f, ensure_ascii=False, indent=2)

        print("💾 数据已保存到文件: {}".format(filename))

        # 同时保存到CSV格式
        save_to_csv(time_point, data)

    except Exception as e:
        print("保存数据失败: {}".format(str(e)))

def save_to_csv(time_point, data):
    """
    保存数据到CSV文件
    """
    try:
        import os

        # CSV文件名
        csv_filename = "gree_stock_data_{}.csv".format(g.target_date.replace('-', ''))

        if g.save_path:
            csv_filepath = os.path.join(g.save_path, csv_filename)
        else:
            csv_filepath = csv_filename

        # 检查文件是否存在，如果不存在则创建并写入表头
        file_exists = os.path.exists(csv_filepath)

        with open(csv_filepath, 'a', encoding='utf-8') as f:
            if not file_exists:
                # 写入表头
                f.write("股票代码,股票名称,日期,时间,时间标签,收盘价,开盘价,最高价,最低价,成交量\n")

            # 写入数据
            f.write("{},{},{},{},{},{:.2f},{:.2f},{:.2f},{:.2f},{:.0f}\n".format(
                g.security,
                '格力电器',
                g.target_date,
                time_point,
                data['time_label'],
                data['close'],
                data['open'],
                data['high'],
                data['low'],
                data['volume']
            ))

        if not file_exists:
            print("📊 CSV文件已创建: {}".format(csv_filename))
        else:
            print("📊 数据已追加到CSV文件: {}".format(csv_filename))

    except Exception as e:
        print("保存CSV失败: {}".format(str(e)))

def save_all_data():
    """
    保存所有收集到的数据到汇总文件
    """
    try:
        import json
        import os
        from datetime import datetime

        # 收集所有数据
        all_data = {
            'stock_code': g.security,
            'stock_name': '格力电器',
            'target_date': g.target_date,
            'collection_summary': {
                'total_targets': len(g.target_times),
                'collected_count': sum(1 for found in g.found_data.values() if found),
                'success_rate': sum(1 for found in g.found_data.values() if found) / len(g.target_times) * 100
            },
            'time_points': []
        }

        # 添加每个时间点的数据
        for time_point in g.target_times:
            time_index = g.target_times.index(time_point)
            time_label = g.time_labels[time_index]

            point_data = {
                'time': time_point,
                'time_label': time_label,
                'collected': g.found_data[time_point],
                'data': g.stock_data[time_point] if g.found_data[time_point] else None
            }
            all_data['time_points'].append(point_data)

        all_data['save_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 保存汇总文件
        summary_filename = "gree_stock_summary_{}.json".format(g.target_date.replace('-', ''))

        if g.save_path:
            summary_filepath = os.path.join(g.save_path, summary_filename)
        else:
            summary_filepath = summary_filename

        with open(summary_filepath, 'w', encoding='utf-8') as f:
            json.dump(all_data, f, ensure_ascii=False, indent=2)

        print("📋 汇总数据已保存到: {}".format(summary_filename))

    except Exception as e:
        print("保存汇总数据失败: {}".format(str(e)))

def search_historical_data(security):
    """
    搜索历史数据中的目标时间点
    """
    try:
        print("正在搜索历史数据中的目标时间点...")
        
        # 获取分钟级历史数据
        history_data = get_history(
            count=600,  # 获取600分钟数据（约3个交易日）
            unit='1m',
            field=['open', 'close', 'high', 'low', 'volume'],
            security_list=security,
            fq='pre',
            include=True
        )
        
        print("获取到{}分钟历史数据".format(len(history_data)))
        
        # 查找所有目标时间点
        found_times = []
        for date_idx in history_data.index:
            date_str = date_idx.strftime('%Y-%m-%d')
            time_str = date_idx.strftime('%H:%M:%S')
            
            if date_str == g.target_date and time_str in g.target_times:
                if not g.found_data[time_str]:
                    # 获取数据
                    close_price = history_data['close'].loc[date_idx]
                    open_price = history_data['open'].loc[date_idx]
                    high_price = history_data['high'].loc[date_idx]
                    low_price = history_data['low'].loc[date_idx]
                    volume = history_data['volume'].loc[date_idx]
                    
                    # 获取时间标签
                    time_index = g.target_times.index(time_str)
                    time_label = g.time_labels[time_index]
                    
                    print("=" * 60)
                    print("*** 在历史数据中找到目标时间点! ***")
                    print("格力电器2025年7月30日{}股价:".format(time_label))
                    print("时间: {} {}".format(date_str, time_str))
                    print("股价(收盘): {:.2f}元".format(close_price))
                    print("开盘价: {:.2f}元".format(open_price))
                    print("最高价: {:.2f}元".format(high_price))
                    print("最低价: {:.2f}元".format(low_price))
                    print("成交量: {:.0f}元".format(volume))
                    print("=" * 60)

                    # 保存数据到字典
                    g.stock_data[time_str] = {
                        'time': "{} {}".format(date_str, time_str),
                        'time_label': time_label,
                        'close': close_price,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'volume': volume
                    }

                    # 标记已找到
                    g.found_data[time_str] = True
                    found_times.append(time_str)

                    # 立即保存到文件
                    save_data_to_file(time_str)
        
        if found_times:
            print("在历史数据中找到{}个目标时间点: {}".format(
                len(found_times), ", ".join(found_times)))
            
            # 检查是否所有时间点都找到了
            all_found = all(g.found_data[time_point] for time_point in g.target_times)
            if all_found:
                print("🎉 所有目标时间点数据已收集完成!")
                print_summary()
        else:
            print("❌ 历史数据中未找到任何目标时间点")
            show_available_data(history_data)
            
    except Exception as e:
        print("获取历史数据失败: {}".format(str(e)))

def show_available_data(history_data):
    """
    显示目标日期的可用数据
    """
    print("目标日期({})的可用分钟数据示例:".format(g.target_date))
    count = 0
    for date_idx in history_data.index:
        date_str = date_idx.strftime('%Y-%m-%d')
        if date_str == g.target_date and count < 10:
            time_str = date_idx.strftime('%H:%M:%S')
            close_price = history_data['close'].loc[date_idx]
            print("{} {}: {:.2f}元".format(date_str, time_str, close_price))
            count += 1
    
    if count == 0:
        print("未找到目标日期的任何数据")

def print_summary():
    """
    打印汇总信息
    """
    print("\n" + "=" * 60)
    print("📊 格力电器2025年7月30日股价汇总")
    print("=" * 60)
    
    for i, time_point in enumerate(g.target_times):
        time_label = g.time_labels[i]
        status = "✅ 已获取" if g.found_data[time_point] else "❌ 未获取"
        print("{}: {}".format(time_label, status))
    
    print("=" * 60)

def before_trading_start(context, data):
    """
    盘前准备函数
    """
    print("🚀 策略启动 - 格力电器多时间点股价查询")
    print("📅 目标日期: {}".format(g.target_date))
    print("⏰ 目标时间点:")
    for i, time_point in enumerate(g.target_times):
        print("   {} - {}".format(g.time_labels[i], time_point))
    print("-" * 50)

def after_trading_end(context, data):
    """
    盘后处理函数
    """
    # 统计结果
    found_count = sum(1 for found in g.found_data.values() if found)
    total_count = len(g.target_times)

    print("\n" + "=" * 60)
    print("📈 最终查询结果统计")
    print("=" * 60)
    print("🎯 目标时间点总数: {}".format(total_count))
    print("✅ 成功获取数据: {}".format(found_count))
    print("📊 成功率: {:.1f}%".format(found_count / total_count * 100))

    print("\n详细状态:")
    for i, time_point in enumerate(g.target_times):
        time_label = g.time_labels[i]
        status = "✅ 已获取" if g.found_data[time_point] else "❌ 未获取"
        print("   {}: {}".format(time_label, status))

    # 保存汇总数据
    if found_count > 0:
        print("\n💾 正在保存汇总数据...")
        save_all_data()
        print("📁 数据保存完成!")

        # 显示保存的文件列表
        print("\n📋 已保存的文件:")
        print("   - CSV汇总文件: gree_stock_data_{}.csv".format(g.target_date.replace('-', '')))
        print("   - JSON汇总文件: gree_stock_summary_{}.json".format(g.target_date.replace('-', '')))

        for time_point in g.target_times:
            if g.found_data[time_point]:
                time_str = time_point.replace(':', '')
                print("   - JSON详细文件: gree_stock_{}_{}.json".format(
                    g.target_date.replace('-', ''), time_str))

    if found_count == total_count:
        print("\n🎉 恭喜! 所有目标时间点数据收集完成!")
    else:
        print("\n⚠️  部分数据未获取，可能原因:")
        print("   1. 2025年7月30日不是交易日")
        print("   2. 回测未设置为分钟级")
        print("   3. 回测时间范围不包含目标时间")
        print("   4. 目标时间点没有交易数据")

    print("=" * 60)

"""
🔧 使用说明:

1. 📋 在Ptrade中新建策略，复制此代码

2. ⚙️  设置回测参数 (重要!):
   - 开始时间: 2025-07-30 09:30:00
   - 结束时间: 2025-07-30 15:00:00  
   - 频率: 分钟级 (必须!)
   - 股票池: 000651.SZ

3. ▶️  运行回测，查看日志输出

4. 📊 预期输出:
   每个时间点显示:
   - 股价(收盘价)
   - 开盘价  
   - 最高价
   - 最低价
   - 成交量

⚠️  注意事项:
- 必须设置为分钟级回测
- 格力电器代码: 000651.SZ  
- 目标日期: 2025-07-30
- 目标时间: 09:35, 09:40, 09:45
"""
